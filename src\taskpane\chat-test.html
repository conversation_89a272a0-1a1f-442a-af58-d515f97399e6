<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Interface Test</title>
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css"/>
    <link href="taskpane.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: #0078d4;
            color: white;
            padding: 1rem;
            text-align: center;
        }
        .mock-office {
            height: 600px;
            position: relative;
        }
    </style>
</head>
<body class="ms-font-m ms-Fabric">
    <div class="test-header">
        <h2>Excel Chat AI - Interface Test</h2>
        <p>Testing the chat interface outside of Excel</p>
    </div>
    
    <div class="test-container">
        <div class="mock-office">
            <!-- Chat interface -->
            <main class="chat-container">
                <!-- Chat header -->
                <header class="chat-header">
                    <div class="chat-header-content">
                        <i class="ms-Icon ms-Icon--Robot ms-font-l chat-icon"></i>
                        <div class="chat-title">
                            <h1 class="ms-font-l">Excel AI Assistant</h1>
                            <span class="ms-font-s chat-status" id="chat-status">Ready to help</span>
                        </div>
                    </div>
                    <button class="ms-Button ms-Button--icon clear-chat-btn" id="clear-chat" title="Clear chat history">
                        <i class="ms-Icon ms-Icon--Delete"></i>
                    </button>
                </header>

                <!-- Chat messages area -->
                <div class="chat-messages" id="chat-messages">
                    <div class="welcome-message">
                        <div class="message ai-message">
                            <div class="message-avatar">
                                <i class="ms-Icon ms-Icon--Robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    Hello! I'm your Excel AI assistant. I can help you with:
                                    <ul>
                                        <li>Analyzing your spreadsheet data</li>
                                        <li>Creating formulas and functions</li>
                                        <li>Generating charts and visualizations</li>
                                        <li>Data manipulation and formatting</li>
                                        <li>Excel tips and best practices</li>
                                    </ul>
                                    What would you like to work on today?
                                </div>
                                <div class="message-time">Just now</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat input area -->
                <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                        <textarea 
                            id="chat-input" 
                            class="chat-input ms-font-m" 
                            placeholder="Ask me anything about your Excel data..."
                            rows="1"
                            maxlength="2000"></textarea>
                        <button 
                            id="send-button" 
                            class="send-button ms-Button ms-Button--primary"
                            title="Send message (Ctrl+Enter)">
                            <i class="ms-Icon ms-Icon--Send"></i>
                        </button>
                    </div>
                    <div class="input-footer">
                        <span class="ms-font-xs">Press Ctrl+Enter to send • <span id="char-count">0</span>/2000</span>
                    </div>
                </div>

                <!-- Typing indicator -->
                <div class="typing-indicator" id="typing-indicator" style="display: none;">
                    <div class="message ai-message">
                        <div class="message-avatar">
                            <i class="ms-Icon ms-Icon--Robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Mock Office.js for testing
        window.Office = {
            onReady: function(callback) {
                callback({ host: { HostType: { Excel: 'Excel' } } });
            },
            HostType: { Excel: 'Excel' }
        };

        // Mock Excel API for testing
        window.Excel = {
            run: async function(callback) {
                const mockContext = {
                    workbook: {
                        getSelectedRange: function() {
                            return {
                                load: function() {},
                                address: "A1:C10",
                                values: [
                                    ["Name", "Age", "Score"],
                                    ["John", 25, 85],
                                    ["Jane", 30, 92],
                                    ["Bob", 22, 78]
                                ],
                                rowCount: 4,
                                columnCount: 3
                            };
                        }
                    },
                    sync: async function() {}
                };
                return await callback(mockContext);
            }
        };
    </script>
    <script src="taskpane.js"></script>
</body>
</html>
