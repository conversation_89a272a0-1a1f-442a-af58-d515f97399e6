/*
 * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
 * Excel Chat AI Add-in JavaScript
 */

/* global console, document, Excel, Office */

// Chat state management
let chatHistory = [];
let isTyping = false;

Office.onReady((info) => {
  if (info.host === Office.HostType.Excel) {
    document.getElementById("sideload-msg").style.display = "none";
    document.getElementById("app-body").style.display = "flex";
    initializeChatInterface();
  }
});

function initializeChatInterface() {
  const chatInput = document.getElementById("chat-input");
  const sendButton = document.getElementById("send-button");
  const clearButton = document.getElementById("clear-chat");

  // Event listeners
  sendButton.addEventListener("click", sendMessage);
  clearButton.addEventListener("click", clearChat);
  chatInput.addEventListener("input", handleInputChange);
  chatInput.addEventListener("keydown", handleKeyDown);

  // Auto-resize textarea
  chatInput.addEventListener("input", autoResizeTextarea);

  // Focus on input
  chatInput.focus();
}

function handleInputChange(event) {
  const input = event.target;
  const charCount = document.getElementById("char-count");
  const sendButton = document.getElementById("send-button");

  charCount.textContent = input.value.length;
  sendButton.disabled = input.value.trim().length === 0 || isTyping;
}

function handleKeyDown(event) {
  if (event.ctrlKey && event.key === "Enter") {
    event.preventDefault();
    sendMessage();
  }
}

function autoResizeTextarea() {
  const textarea = document.getElementById("chat-input");
  textarea.style.height = "auto";
  textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px";
}

async function sendMessage() {
  const chatInput = document.getElementById("chat-input");
  const message = chatInput.value.trim();

  if (!message || isTyping) return;

  // Add user message to chat
  addMessage(message, "user");

  // Clear input
  chatInput.value = "";
  chatInput.style.height = "auto";
  document.getElementById("char-count").textContent = "0";
  document.getElementById("send-button").disabled = true;

  // Show typing indicator
  showTypingIndicator();

  try {
    // Simulate AI response (replace with actual AI integration)
    const response = await generateAIResponse(message);
    hideTypingIndicator();
    addMessage(response, "ai");
  } catch (error) {
    hideTypingIndicator();
    addMessage("Sorry, I encountered an error. Please try again.", "ai");
    console.error("Error generating AI response:", error);
  }

  // Focus back on input
  chatInput.focus();
}

function addMessage(text, sender) {
  const messagesContainer = document.getElementById("chat-messages");
  const messageDiv = document.createElement("div");
  const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  messageDiv.className = `message ${sender}-message`;
  messageDiv.innerHTML = `
    <div class="message-avatar">
      <i class="ms-Icon ms-Icon--${sender === 'user' ? 'Contact' : 'Robot'}"></i>
    </div>
    <div class="message-content">
      <div class="message-text">${formatMessage(text)}</div>
      <div class="message-time">${timestamp}</div>
    </div>
  `;

  messagesContainer.appendChild(messageDiv);
  messagesContainer.scrollTop = messagesContainer.scrollHeight;

  // Add to chat history
  chatHistory.push({ text, sender, timestamp });
}

function formatMessage(text) {
  // Basic formatting for AI responses
  return text
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>');
}

function showTypingIndicator() {
  isTyping = true;
  const typingIndicator = document.getElementById("typing-indicator");
  const chatStatus = document.getElementById("chat-status");

  typingIndicator.style.display = "block";
  chatStatus.textContent = "Thinking...";

  const messagesContainer = document.getElementById("chat-messages");
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function hideTypingIndicator() {
  isTyping = false;
  const typingIndicator = document.getElementById("typing-indicator");
  const chatStatus = document.getElementById("chat-status");

  typingIndicator.style.display = "none";
  chatStatus.textContent = "Ready to help";
}

function clearChat() {
  if (confirm("Are you sure you want to clear the chat history?")) {
    const messagesContainer = document.getElementById("chat-messages");

    // Remove all messages except welcome message
    const welcomeMessage = messagesContainer.querySelector(".welcome-message");
    messagesContainer.innerHTML = "";
    if (welcomeMessage) {
      messagesContainer.appendChild(welcomeMessage);
    }

    chatHistory = [];
    document.getElementById("chat-input").focus();
  }
}

// AI Response Generation (placeholder - replace with actual AI integration)
async function generateAIResponse(userMessage) {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  const lowerMessage = userMessage.toLowerCase();

  // Excel-specific responses
  if (lowerMessage.includes("formula") || lowerMessage.includes("function")) {
    return await handleFormulaRequest(userMessage);
  } else if (lowerMessage.includes("chart") || lowerMessage.includes("graph")) {
    return "I can help you create charts! First, select the data range you want to visualize, then I can guide you through creating the perfect chart type for your data.";
  } else if (lowerMessage.includes("data") || lowerMessage.includes("analyze")) {
    return await analyzeCurrentData();
  } else if (lowerMessage.includes("format") || lowerMessage.includes("style")) {
    return "I can help you format your spreadsheet! What specific formatting would you like to apply? I can help with:\n• Cell formatting (colors, fonts, borders)\n• Number formatting (currency, dates, percentages)\n• Conditional formatting\n• Table styles";
  } else {
    return "I'm here to help with your Excel tasks! I can assist with formulas, data analysis, charts, formatting, and more. What specific Excel challenge are you working on?";
  }
}

// Excel-specific helper functions
async function handleFormulaRequest(userMessage) {
  try {
    return await Excel.run(async (context) => {
      const range = context.workbook.getSelectedRange();
      range.load("address");
      await context.sync();

      let response = `I can help you with formulas! You currently have selected: ${range.address}. `;

      // Provide specific help based on user's question
      if (userMessage.toLowerCase().includes("sum")) {
        response += "For SUM formulas:\n• =SUM(A1:A10) - Add up a range\n• =SUM(A1,C1,E1) - Add specific cells";
      } else if (userMessage.toLowerCase().includes("average")) {
        response += "For AVERAGE formulas:\n• =AVERAGE(A1:A10) - Calculate mean\n• =AVERAGEIF(A1:A10,\">5\") - Conditional average";
      } else {
        response += "Here are some common formulas you might need:";
      }

      response += `

**Basic Math:**
• =SUM(A1:A10) - Add up values
• =AVERAGE(A1:A10) - Calculate average
• =COUNT(A1:A10) - Count numbers

**Text Functions:**
• =CONCATENATE(A1,B1) - Join text
• =LEFT(A1,5) - Get first 5 characters
• =UPPER(A1) - Convert to uppercase

**Lookup Functions:**
• =VLOOKUP(value,table,column,FALSE) - Find data
• =INDEX(MATCH()) - Advanced lookup

What specific formula do you need help with?`;

      return response;

**Basic Math:**
• =SUM(A1:A10) - Add up values
• =AVERAGE(A1:A10) - Calculate average
• =COUNT(A1:A10) - Count numbers

**Text Functions:**
• =CONCATENATE(A1,B1) - Join text
• =LEFT(A1,5) - Get first 5 characters
• =UPPER(A1) - Convert to uppercase

**Lookup Functions:**
• =VLOOKUP(value,table,column,FALSE) - Find data
• =INDEX(MATCH()) - Advanced lookup

What specific formula do you need help with?`;
    });
  } catch (error) {
    return "I can help you with Excel formulas! What type of calculation or function are you trying to create?";
  }
}

async function analyzeCurrentData() {
  try {
    return await Excel.run(async (context) => {
      const range = context.workbook.getSelectedRange();
      range.load(["address", "values", "rowCount", "columnCount"]);
      await context.sync();

      const rowCount = range.rowCount;
      const colCount = range.columnCount;
      const values = range.values;

      // Basic analysis
      let hasNumbers = false;
      let hasText = false;
      let numberCount = 0;

      for (let i = 0; i < values.length; i++) {
        for (let j = 0; j < values[i].length; j++) {
          const value = values[i][j];
          if (typeof value === 'number' && !isNaN(value)) {
            hasNumbers = true;
            numberCount++;
          } else if (typeof value === 'string' && value.trim() !== '') {
            hasText = true;
          }
        }
      }

      let analysis = `**Data Analysis for ${range.address}:**\n\n`;
      analysis += `• **Size:** ${rowCount} rows × ${colCount} columns (${rowCount * colCount} total cells)\n`;
      analysis += `• **Numbers found:** ${numberCount} numeric values\n`;
      analysis += `• **Contains text:** ${hasText ? 'Yes' : 'No'}\n\n`;

      if (hasNumbers) {
        analysis += "**Suggestions:**\n";
        analysis += "• Create a chart to visualize your data\n";
        analysis += "• Use SUM, AVERAGE, or COUNT functions\n";
        analysis += "• Apply conditional formatting to highlight patterns\n";
        analysis += "• Consider creating a pivot table for deeper analysis";
      } else {
        analysis += "**Suggestions:**\n";
        analysis += "• Use text functions like CONCATENATE or SPLIT\n";
        analysis += "• Apply data validation for consistency\n";
        analysis += "• Consider formatting as a table for better organization";
      }

      return analysis;
    });
  } catch (error) {
    return "I'd love to analyze your data! Please select a range of cells first, then ask me to analyze it again.";
  }
}

// Export functions for testing
export { generateAIResponse, handleFormulaRequest, analyzeCurrentData };
