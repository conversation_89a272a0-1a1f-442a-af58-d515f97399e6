/*
 * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
 * Excel Chat AI Add-in JavaScript
 */

/* global console, document, Excel, Office */

// Import AI integration (will be bundled by webpack)
// import AIIntegration from './ai-integration.js';

// Chat state management
let chatHistory = [];
let isTyping = false;
let aiIntegration = null;

Office.onReady((info) => {
  if (info.host === Office.HostType.Excel) {
    document.getElementById("sideload-msg").style.display = "none";
    document.getElementById("app-body").style.display = "flex";
    initializeChatInterface();
    initializeAI();
  }
});

function initializeChatInterface() {
  const chatInput = document.getElementById("chat-input");
  const sendButton = document.getElementById("send-button");
  const clearButton = document.getElementById("clear-chat");

  // Event listeners
  sendButton.addEventListener("click", sendMessage);
  clearButton.addEventListener("click", clearChat);
  chatInput.addEventListener("input", handleInputChange);
  chatInput.addEventListener("keydown", handleKeyDown);

  // Auto-resize textarea
  chatInput.addEventListener("input", autoResizeTextarea);

  // Focus on input
  chatInput.focus();
}

function initializeAI() {
  // Initialize AI integration with mock provider for demo
  // In production, you would configure with real API keys
  try {
    // For now, we'll use the mock AI since we don't have real API keys
    // aiIntegration = new AIIntegration({
    //   provider: 'OPENAI_GPT4',
    //   apiKey: 'your-api-key-here',
    //   model: 'gpt-4'
    // });

    // Using mock for demonstration
    aiIntegration = {
      provider: 'MOCK',
      sendToAI: mockSendToAI
    };

    console.log('AI integration initialized');
  } catch (error) {
    console.error('Failed to initialize AI:', error);
    aiIntegration = { provider: 'FALLBACK', sendToAI: fallbackAI };
  }
}

function handleInputChange(event) {
  const input = event.target;
  const charCount = document.getElementById("char-count");
  const sendButton = document.getElementById("send-button");

  charCount.textContent = input.value.length;
  sendButton.disabled = input.value.trim().length === 0 || isTyping;
}

function handleKeyDown(event) {
  if (event.ctrlKey && event.key === "Enter") {
    event.preventDefault();
    sendMessage();
  }
}

function autoResizeTextarea() {
  const textarea = document.getElementById("chat-input");
  textarea.style.height = "auto";
  textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px";
}

async function sendMessage() {
  const chatInput = document.getElementById("chat-input");
  const message = chatInput.value.trim();

  if (!message || isTyping) return;

  // Add user message to chat
  addMessage(message, "user");

  // Clear input
  chatInput.value = "";
  chatInput.style.height = "auto";
  document.getElementById("char-count").textContent = "0";
  document.getElementById("send-button").disabled = true;

  // Show typing indicator
  showTypingIndicator();

  try {
    // Use the new AI integration system
    const response = await aiIntegration.sendToAI(message, {
      includeFullWorkbook: false,
      strategy: 'smart'
    });

    hideTypingIndicator();

    if (response.success) {
      addMessage(response.content, "ai");

      // Log metadata for debugging
      if (response.metadata) {
        console.log('AI Response Metadata:', response.metadata);
      }
    } else {
      addMessage(response.content || "Sorry, I encountered an error. Please try again.", "ai");
      console.error("AI Error:", response.error);
    }
  } catch (error) {
    hideTypingIndicator();
    addMessage("Sorry, I encountered an error. Please try again.", "ai");
    console.error("Error generating AI response:", error);
  }

  // Focus back on input
  chatInput.focus();
}

function addMessage(text, sender) {
  const messagesContainer = document.getElementById("chat-messages");
  const messageDiv = document.createElement("div");
  const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  messageDiv.className = `message ${sender}-message`;
  messageDiv.innerHTML = `
    <div class="message-avatar">
      <i class="ms-Icon ms-Icon--${sender === 'user' ? 'Contact' : 'Robot'}"></i>
    </div>
    <div class="message-content">
      <div class="message-text">${formatMessage(text)}</div>
      <div class="message-time">${timestamp}</div>
    </div>
  `;

  messagesContainer.appendChild(messageDiv);
  messagesContainer.scrollTop = messagesContainer.scrollHeight;

  // Add to chat history
  chatHistory.push({ text, sender, timestamp });
}

function formatMessage(text) {
  // Basic formatting for AI responses
  return text
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>');
}

function showTypingIndicator() {
  isTyping = true;
  const typingIndicator = document.getElementById("typing-indicator");
  const chatStatus = document.getElementById("chat-status");

  typingIndicator.style.display = "block";
  chatStatus.textContent = "Thinking...";

  const messagesContainer = document.getElementById("chat-messages");
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function hideTypingIndicator() {
  isTyping = false;
  const typingIndicator = document.getElementById("typing-indicator");
  const chatStatus = document.getElementById("chat-status");

  typingIndicator.style.display = "none";
  chatStatus.textContent = "Ready to help";
}

function clearChat() {
  if (confirm("Are you sure you want to clear the chat history?")) {
    const messagesContainer = document.getElementById("chat-messages");

    // Remove all messages except welcome message
    const welcomeMessage = messagesContainer.querySelector(".welcome-message");
    messagesContainer.innerHTML = "";
    if (welcomeMessage) {
      messagesContainer.appendChild(welcomeMessage);
    }

    chatHistory = [];
    document.getElementById("chat-input").focus();
  }
}

// Mock AI integration for demonstration
async function mockSendToAI(userMessage, options = {}) {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  try {
    // Extract Excel data context
    const includeContext = options.includeFullWorkbook || false;
    const dataContext = await extractSelectedData(includeContext);
    const lowerMessage = userMessage.toLowerCase();

    let response = "";

    // Excel-specific responses based on user intent
    if (lowerMessage.includes("formula") || lowerMessage.includes("function")) {
      response = await generateFormulaResponse(userMessage, dataContext);
    } else if (lowerMessage.includes("chart") || lowerMessage.includes("graph")) {
      response = await generateChartResponse(userMessage, dataContext);
    } else if (lowerMessage.includes("data") || lowerMessage.includes("analyze")) {
      response = await generateAnalysisResponse(userMessage, dataContext);
    } else if (lowerMessage.includes("format") || lowerMessage.includes("style")) {
      response = generateFormattingResponse(userMessage, dataContext);
    } else {
      response = generateGeneralResponse(userMessage, dataContext);
    }

    return {
      success: true,
      content: response,
      metadata: {
        provider: 'MOCK',
        dataSize: dataContext ? JSON.stringify(dataContext).length : 0,
        responseLength: response.length,
        timestamp: new Date().toISOString()
      }
    };
  } catch (error) {
    return {
      success: false,
      content: "I'm having trouble accessing your Excel data. Please make sure you have a range selected and try again.",
      error: error.message
    };
  }
}

async function generateFormulaResponse(userMessage, dataContext) {
  let response = "**Formula Assistance**\n\n";

  if (dataContext && dataContext.selection) {
    const { address, dimensions } = dataContext.selection;
    response += `Based on your selected range **${address}** (${dimensions.rows} rows × ${dimensions.columns} columns):\n\n`;

    if (userMessage.toLowerCase().includes('sum')) {
      response += `**SUM Formulas:**\n• \`=SUM(${address})\` - Add all values in your selection\n• \`=SUMIF(${address},">0")\` - Sum only positive values\n• \`=SUMPRODUCT()\` - For complex calculations\n\n`;
    } else if (userMessage.toLowerCase().includes('average')) {
      response += `**AVERAGE Formulas:**\n• \`=AVERAGE(${address})\` - Calculate mean of your data\n• \`=AVERAGEIF(${address},">0")\` - Average excluding zeros\n• \`=MEDIAN(${address})\` - Find the middle value\n\n`;
    } else {
      response += `**Common Formulas for Your Data:**\n• \`=SUM(${address})\` - Add up values\n• \`=AVERAGE(${address})\` - Calculate average\n• \`=COUNT(${address})\` - Count numbers\n• \`=MAX(${address})\` - Find maximum\n• \`=MIN(${address})\` - Find minimum\n\n`;
    }
  }

  response += `**Advanced Options:**\n• **VLOOKUP**: \`=VLOOKUP(lookup_value, table_array, col_index, FALSE)\`\n• **IF Statements**: \`=IF(condition, value_if_true, value_if_false)\`\n• **CONCATENATE**: \`=CONCATENATE(text1, text2)\` or use \`&\`\n\nWhat specific calculation do you need help with?`;

  return response;
}

async function generateChartResponse(_, dataContext) {
  let response = "**Chart & Visualization Recommendations**\n\n";

  if (dataContext && dataContext.selection) {
    const { dimensions, dataTypes } = dataContext.selection;

    response += `For your **${dimensions.rows} × ${dimensions.columns}** data selection:\n\n`;

    if (dataTypes && dataTypes.numbers > 0) {
      response += `**Recommended Chart Types:**\n`;

      if (dimensions.columns >= 2) {
        response += `• **Column Chart** - Compare values across categories\n`;
        response += `• **Line Chart** - Show trends over time\n`;
        response += `• **Scatter Plot** - Explore relationships between variables\n\n`;
      } else {
        response += `• **Histogram** - Show data distribution\n`;
        response += `• **Box Plot** - Display data quartiles\n\n`;
      }
    }

    response += `**Steps to Create Charts:**\n1. Keep your data selected\n2. Go to **Insert** > **Charts**\n3. Choose your chart type\n4. Customize with **Chart Tools**\n\n`;
  }

  response += `**Pro Tips:**\n• Include headers for better labels\n• Use consistent data types in columns\n• Consider data ranges for better visualization\n• Add titles and axis labels for clarity`;

  return response;
}

async function generateAnalysisResponse(_, dataContext) {
  let response = "**Data Analysis Results**\n\n";

  if (dataContext && dataContext.selection) {
    const { address, dimensions, dataTypes } = dataContext.selection;

    response += `**Your Data Overview:**\n`;
    response += `• **Range**: ${address}\n`;
    response += `• **Size**: ${dimensions.rows} rows × ${dimensions.columns} columns\n`;

    if (dataTypes) {
      response += `• **Data Types**: ${dataTypes.numbers} numbers, ${dataTypes.text} text cells\n`;
      response += `• **Completeness**: ${Math.round((dataTypes.numbers + dataTypes.text) / (dimensions.rows * dimensions.columns) * 100)}% filled\n\n`;
    }

    if (dataTypes && dataTypes.numbers > 0) {
      response += `**Analysis Suggestions:**\n`;
      response += `• Use **Pivot Tables** for deeper insights\n`;
      response += `• Apply **Conditional Formatting** to highlight patterns\n`;
      response += `• Create **Summary Statistics** with formulas\n`;
      response += `• Consider **Data Validation** for consistency\n\n`;

      response += `**Quick Analysis Formulas:**\n`;
      response += `• \`=AVERAGE(${address})\` - Mean value\n`;
      response += `• \`=STDEV(${address})\` - Standard deviation\n`;
      response += `• \`=CORREL(range1, range2)\` - Correlation\n`;
    }
  }

  response += `\n**Next Steps:**\n• Select specific data ranges for focused analysis\n• Ask about specific statistical measures\n• Request help with pivot tables or charts`;

  return response;
}

function generateFormattingResponse(_, dataContext) {
  let response = "**Formatting & Styling Help**\n\n";

  if (dataContext && dataContext.selection) {
    const { address } = dataContext.selection;
    response += `For your selected range **${address}**:\n\n`;
  }

  response += `**Common Formatting Options:**\n`;
  response += `• **Number Formats**: Currency, percentage, dates\n`;
  response += `• **Cell Styles**: Colors, fonts, borders\n`;
  response += `• **Conditional Formatting**: Highlight based on values\n`;
  response += `• **Table Formatting**: Convert to Excel table\n\n`;

  response += `**Quick Actions:**\n`;
  response += `• **Ctrl+1** - Open Format Cells dialog\n`;
  response += `• **Home** tab - Access formatting tools\n`;
  response += `• **Format as Table** - Apply professional styling\n`;
  response += `• **Conditional Formatting** - Auto-highlight patterns\n\n`;

  response += `What specific formatting would you like to apply?`;

  return response;
}

function generateGeneralResponse(_, dataContext) {
  let response = "**Excel AI Assistant**\n\n";

  if (dataContext && dataContext.selection) {
    const { address, dimensions } = dataContext.selection;
    response += `I can see you have **${address}** selected (${dimensions.rows} rows × ${dimensions.columns} columns).\n\n`;
  }

  response += `**I can help you with:**\n`;
  response += `• **Formulas & Functions** - SUM, AVERAGE, VLOOKUP, IF statements\n`;
  response += `• **Data Analysis** - Statistics, patterns, insights\n`;
  response += `• **Charts & Visualizations** - Graphs, charts, dashboards\n`;
  response += `• **Formatting & Styling** - Colors, fonts, conditional formatting\n`;
  response += `• **Best Practices** - Tips for better spreadsheet organization\n\n`;

  response += `**Popular Questions:**\n`;
  response += `• "Help me create a formula to calculate..."\n`;
  response += `• "Analyze my data and show insights"\n`;
  response += `• "What chart should I use for this data?"\n`;
  response += `• "How do I format this data better?"\n\n`;

  response += `What Excel task can I help you with today?`;

  return response;
}

// Fallback AI for when initialization fails
async function fallbackAI(userMessage) {
  return {
    success: true,
    content: "I'm running in basic mode. I can provide general Excel help, but for advanced features, please check your AI configuration.",
    metadata: { provider: 'FALLBACK' }
  };
}

// Excel-specific helper functions
async function handleFormulaRequest(userMessage) {
  try {
    return await Excel.run(async (context) => {
      const range = context.workbook.getSelectedRange();
      range.load("address");
      await context.sync();

      let response = `I can help you with formulas! You currently have selected: ${range.address}. `;

      // Provide specific help based on user's question
      if (userMessage.toLowerCase().includes("sum")) {
        response += "For SUM formulas:\n• =SUM(A1:A10) - Add up a range\n• =SUM(A1,C1,E1) - Add specific cells";
      } else if (userMessage.toLowerCase().includes("average")) {
        response += "For AVERAGE formulas:\n• =AVERAGE(A1:A10) - Calculate mean\n• =AVERAGEIF(A1:A10,\">5\") - Conditional average";
      } else {
        response += "Here are some common formulas you might need:";
      }

      response += `

**Basic Math:**
• =SUM(A1:A10) - Add up values
• =AVERAGE(A1:A10) - Calculate average
• =COUNT(A1:A10) - Count numbers

**Text Functions:**
• =CONCATENATE(A1,B1) - Join text
• =LEFT(A1,5) - Get first 5 characters
• =UPPER(A1) - Convert to uppercase

**Lookup Functions:**
• =VLOOKUP(value,table,column,FALSE) - Find data
• =INDEX(MATCH()) - Advanced lookup

What specific formula do you need help with?`;

      return response;
    });
  } catch (error) {
    return "I can help you with Excel formulas! What type of calculation or function are you trying to create?";
  }
}

async function analyzeCurrentData() {
  try {
    return await Excel.run(async (context) => {
      const range = context.workbook.getSelectedRange();
      range.load(["address", "values", "rowCount", "columnCount"]);
      await context.sync();

      const rowCount = range.rowCount;
      const colCount = range.columnCount;
      const values = range.values;

      // Basic analysis
      let hasNumbers = false;
      let hasText = false;
      let numberCount = 0;

      for (let i = 0; i < values.length; i++) {
        for (let j = 0; j < values[i].length; j++) {
          const value = values[i][j];
          if (typeof value === 'number' && !isNaN(value)) {
            hasNumbers = true;
            numberCount++;
          } else if (typeof value === 'string' && value.trim() !== '') {
            hasText = true;
          }
        }
      }

      let analysis = `**Data Analysis for ${range.address}:**\n\n`;
      analysis += `• **Size:** ${rowCount} rows × ${colCount} columns (${rowCount * colCount} total cells)\n`;
      analysis += `• **Numbers found:** ${numberCount} numeric values\n`;
      analysis += `• **Contains text:** ${hasText ? 'Yes' : 'No'}\n\n`;

      if (hasNumbers) {
        analysis += "**Suggestions:**\n";
        analysis += "• Create a chart to visualize your data\n";
        analysis += "• Use SUM, AVERAGE, or COUNT functions\n";
        analysis += "• Apply conditional formatting to highlight patterns\n";
        analysis += "• Consider creating a pivot table for deeper analysis";
      } else {
        analysis += "**Suggestions:**\n";
        analysis += "• Use text functions like CONCATENATE or SPLIT\n";
        analysis += "• Apply data validation for consistency\n";
        analysis += "• Consider formatting as a table for better organization";
      }

      return analysis;
    });
  } catch (error) {
    return "I'd love to analyze your data! Please select a range of cells first, then ask me to analyze it again.";
  }
}

// Advanced Excel data extraction functions
async function extractWorkbookContext() {
  try {
    return await Excel.run(async (context) => {
      const workbook = context.workbook;
      const worksheets = workbook.worksheets;
      worksheets.load("items/name");

      const activeSheet = workbook.worksheets.getActiveWorksheet();
      activeSheet.load(["name", "tabColor"]);

      await context.sync();

      return {
        worksheetNames: worksheets.items.map(ws => ws.name),
        activeWorksheet: activeSheet.name,
        workbookStructure: await getWorkbookStructure(context)
      };
    });
  } catch (error) {
    console.error("Error extracting workbook context:", error);
    return null;
  }
}

async function getWorkbookStructure(context) {
  const activeSheet = context.workbook.worksheets.getActiveWorksheet();
  const usedRange = activeSheet.getUsedRange();

  try {
    usedRange.load(["address", "rowCount", "columnCount", "values", "formulas"]);
    await context.sync();

    return {
      usedRange: usedRange.address,
      dimensions: {
        rows: usedRange.rowCount,
        columns: usedRange.columnCount
      },
      hasFormulas: usedRange.formulas.some(row =>
        row.some(cell => cell && cell.toString().startsWith('='))
      ),
      dataTypes: analyzeDataTypes(usedRange.values)
    };
  } catch (error) {
    return { usedRange: "No data", dimensions: { rows: 0, columns: 0 } };
  }
}

function analyzeDataTypes(values) {
  const types = { numbers: 0, text: 0, dates: 0, formulas: 0, empty: 0 };

  values.forEach(row => {
    row.forEach(cell => {
      if (cell === null || cell === undefined || cell === "") {
        types.empty++;
      } else if (typeof cell === 'number') {
        types.numbers++;
      } else if (cell instanceof Date) {
        types.dates++;
      } else if (typeof cell === 'string') {
        if (cell.startsWith('=')) {
          types.formulas++;
        } else {
          types.text++;
        }
      }
    });
  });

  return types;
}

async function extractSelectedData(includeContext = true) {
  try {
    return await Excel.run(async (context) => {
      const range = context.workbook.getSelectedRange();
      range.load([
        "address", "values", "formulas", "numberFormat",
        "rowCount", "columnCount", "text"
      ]);

      let contextData = {};
      if (includeContext) {
        contextData = await extractWorkbookContext();
      }

      await context.sync();

      return {
        selection: {
          address: range.address,
          values: range.values,
          formulas: range.formulas,
          text: range.text,
          dimensions: {
            rows: range.rowCount,
            columns: range.columnCount
          },
          formats: range.numberFormat
        },
        context: contextData,
        metadata: {
          timestamp: new Date().toISOString(),
          totalCells: range.rowCount * range.columnCount,
          dataTypes: analyzeDataTypes(range.values)
        }
      };
    });
  } catch (error) {
    console.error("Error extracting selected data:", error);
    return null;
  }
}

// Export functions for testing
export { mockSendToAI, handleFormulaRequest, analyzeCurrentData, extractWorkbookContext, extractSelectedData };
