/*
 * Excel Data Processing for AI Integration
 * Handles data extraction, formatting, and optimization for AI consumption
 */

// Data size limits for different AI services
const AI_LIMITS = {
  OPENAI_GPT4: { maxTokens: 128000, maxChars: 400000 },
  AZURE_AI: { maxTokens: 32000, maxChars: 100000 },
  CLAUDE: { maxTokens: 200000, maxChars: 600000 },
  GEMINI: { maxTokens: 1000000, maxChars: 3000000 }
};

class ExcelDataProcessor {
  constructor(aiProvider = 'OPENAI_GPT4') {
    this.limits = AI_LIMITS[aiProvider];
    this.compressionStrategies = ['sample', 'summarize', 'chunk'];
  }

  // Main method to prepare Excel data for AI
  async prepareDataForAI(userMessage, options = {}) {
    const {
      includeFullWorkbook = false,
      maxRows = 1000,
      maxColumns = 50,
      strategy = 'smart'
    } = options;

    try {
      let dataPackage;
      
      if (strategy === 'smart') {
        dataPackage = await this.smartDataExtraction(userMessage);
      } else if (includeFullWorkbook) {
        dataPackage = await this.extractFullWorkbook(maxRows, maxColumns);
      } else {
        dataPackage = await this.extractSelectedData();
      }

      // Optimize data size for AI consumption
      const optimizedData = this.optimizeForAI(dataPackage);
      
      return {
        success: true,
        data: optimizedData,
        metadata: {
          originalSize: this.calculateDataSize(dataPackage),
          optimizedSize: this.calculateDataSize(optimizedData),
          strategy: strategy,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error preparing data for AI:', error);
      return {
        success: false,
        error: error.message,
        fallback: this.createFallbackData(userMessage)
      };
    }
  }

  // Smart extraction based on user intent
  async smartDataExtraction(userMessage) {
    const intent = this.analyzeUserIntent(userMessage);
    
    return await Excel.run(async (context) => {
      let dataPackage = {
        intent: intent,
        workbookContext: {},
        primaryData: {},
        supportingData: {}
      };

      // Always get basic context
      dataPackage.workbookContext = await this.getBasicContext(context);

      switch (intent.type) {
        case 'formula':
          dataPackage.primaryData = await this.extractFormulaContext(context, intent);
          break;
        case 'analysis':
          dataPackage.primaryData = await this.extractAnalysisData(context, intent);
          break;
        case 'chart':
          dataPackage.primaryData = await this.extractChartData(context, intent);
          break;
        case 'format':
          dataPackage.primaryData = await this.extractFormattingData(context, intent);
          break;
        default:
          dataPackage.primaryData = await this.extractSelectedRange(context);
      }

      return dataPackage;
    });
  }

  analyzeUserIntent(message) {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('formula') || lowerMessage.includes('function') || lowerMessage.includes('calculate')) {
      return { type: 'formula', confidence: 0.9, keywords: ['formula', 'function', 'calculate'] };
    } else if (lowerMessage.includes('analyze') || lowerMessage.includes('summary') || lowerMessage.includes('statistics')) {
      return { type: 'analysis', confidence: 0.8, keywords: ['analyze', 'summary', 'statistics'] };
    } else if (lowerMessage.includes('chart') || lowerMessage.includes('graph') || lowerMessage.includes('visualize')) {
      return { type: 'chart', confidence: 0.9, keywords: ['chart', 'graph', 'visualize'] };
    } else if (lowerMessage.includes('format') || lowerMessage.includes('style') || lowerMessage.includes('color')) {
      return { type: 'format', confidence: 0.7, keywords: ['format', 'style', 'color'] };
    }
    
    return { type: 'general', confidence: 0.5, keywords: [] };
  }

  async getBasicContext(context) {
    const workbook = context.workbook;
    const activeSheet = workbook.worksheets.getActiveWorksheet();
    
    activeSheet.load(['name', 'tabColor']);
    const worksheets = workbook.worksheets;
    worksheets.load('items/name');
    
    await context.sync();
    
    return {
      activeSheet: activeSheet.name,
      worksheetCount: worksheets.items.length,
      worksheetNames: worksheets.items.map(ws => ws.name).slice(0, 10) // Limit to first 10
    };
  }

  async extractSelectedRange(context) {
    const range = context.workbook.getSelectedRange();
    range.load(['address', 'values', 'formulas', 'rowCount', 'columnCount']);
    await context.sync();
    
    return {
      type: 'selection',
      address: range.address,
      dimensions: { rows: range.rowCount, columns: range.columnCount },
      values: this.limitArraySize(range.values, 100, 20),
      formulas: this.limitArraySize(range.formulas, 100, 20),
      summary: this.generateDataSummary(range.values)
    };
  }

  async extractAnalysisData(context, intent) {
    const range = context.workbook.getSelectedRange();
    range.load(['address', 'values', 'rowCount', 'columnCount']);
    await context.sync();
    
    const values = range.values;
    const numericData = this.extractNumericColumns(values);
    
    return {
      type: 'analysis',
      address: range.address,
      dimensions: { rows: range.rowCount, columns: range.columnCount },
      sample: this.sampleData(values, 50), // First 50 rows
      statistics: this.calculateBasicStats(numericData),
      dataTypes: this.analyzeColumnTypes(values),
      summary: this.generateDataSummary(values)
    };
  }

  // Optimize data package for AI consumption
  optimizeForAI(dataPackage) {
    const currentSize = this.calculateDataSize(dataPackage);
    
    if (currentSize <= this.limits.maxChars) {
      return dataPackage; // No optimization needed
    }

    // Apply compression strategies
    let optimized = JSON.parse(JSON.stringify(dataPackage)); // Deep clone
    
    // Strategy 1: Reduce data precision
    optimized = this.reducePrecision(optimized);
    
    // Strategy 2: Sample large datasets
    optimized = this.applySampling(optimized);
    
    // Strategy 3: Summarize repetitive data
    optimized = this.summarizeData(optimized);
    
    return optimized;
  }

  limitArraySize(array, maxRows, maxCols) {
    if (!array || !Array.isArray(array)) return array;
    
    return array.slice(0, maxRows).map(row => 
      Array.isArray(row) ? row.slice(0, maxCols) : row
    );
  }

  sampleData(values, maxRows) {
    if (!values || values.length <= maxRows) return values;
    
    const step = Math.ceil(values.length / maxRows);
    const sampled = [];
    
    // Always include header if it exists
    if (values.length > 0) sampled.push(values[0]);
    
    // Sample remaining rows
    for (let i = 1; i < values.length; i += step) {
      if (sampled.length < maxRows) {
        sampled.push(values[i]);
      }
    }
    
    return sampled;
  }

  extractNumericColumns(values) {
    if (!values || values.length === 0) return [];
    
    const numericColumns = [];
    const columnCount = values[0].length;
    
    for (let col = 0; col < columnCount; col++) {
      const columnData = values.slice(1).map(row => row[col]).filter(val => 
        typeof val === 'number' && !isNaN(val)
      );
      
      if (columnData.length > 0) {
        numericColumns.push({
          index: col,
          header: values[0] ? values[0][col] : `Column ${col + 1}`,
          data: columnData.slice(0, 1000) // Limit to 1000 values
        });
      }
    }
    
    return numericColumns;
  }

  calculateBasicStats(numericColumns) {
    return numericColumns.map(col => ({
      column: col.header,
      count: col.data.length,
      min: Math.min(...col.data),
      max: Math.max(...col.data),
      avg: col.data.reduce((a, b) => a + b, 0) / col.data.length,
      sum: col.data.reduce((a, b) => a + b, 0)
    }));
  }

  generateDataSummary(values) {
    if (!values || values.length === 0) return "No data";
    
    const totalCells = values.length * (values[0]?.length || 0);
    const nonEmptyCells = values.flat().filter(cell => 
      cell !== null && cell !== undefined && cell !== ""
    ).length;
    
    return {
      totalRows: values.length,
      totalColumns: values[0]?.length || 0,
      totalCells: totalCells,
      nonEmptyCells: nonEmptyCells,
      fillRate: Math.round((nonEmptyCells / totalCells) * 100)
    };
  }

  calculateDataSize(data) {
    return JSON.stringify(data).length;
  }

  reducePrecision(data) {
    // Reduce number precision to save space
    const reduceNumbers = (obj) => {
      if (typeof obj === 'number') {
        return Math.round(obj * 1000) / 1000; // 3 decimal places
      } else if (Array.isArray(obj)) {
        return obj.map(reduceNumbers);
      } else if (typeof obj === 'object' && obj !== null) {
        const reduced = {};
        for (const [key, value] of Object.entries(obj)) {
          reduced[key] = reduceNumbers(value);
        }
        return reduced;
      }
      return obj;
    };
    
    return reduceNumbers(data);
  }

  applySampling(data) {
    // Further reduce data if still too large
    if (data.primaryData && data.primaryData.values) {
      data.primaryData.values = this.sampleData(data.primaryData.values, 100);
    }
    return data;
  }

  summarizeData(data) {
    // Convert large arrays to summaries
    if (this.calculateDataSize(data) > this.limits.maxChars * 0.8) {
      if (data.primaryData && data.primaryData.values) {
        const summary = this.generateDataSummary(data.primaryData.values);
        data.primaryData.summary = summary;
        data.primaryData.values = this.sampleData(data.primaryData.values, 20);
      }
    }
    return data;
  }

  createFallbackData(userMessage) {
    return {
      type: 'fallback',
      message: userMessage,
      context: 'Unable to extract Excel data',
      suggestion: 'Please select a range of cells and try again'
    };
  }
}

// Export the processor class
export default ExcelDataProcessor;
