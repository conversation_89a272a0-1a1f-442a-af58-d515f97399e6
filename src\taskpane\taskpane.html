<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. -->
<!-- Excel Chat AI Add-in - Chat Room Interface -->

<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Excel Chat AI</title>

    <!-- Office JavaScript API -->
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>

    <!-- For more information on Fluent UI, visit https://developer.microsoft.com/fluentui#/. -->
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css"/>

    <!-- Template styles -->
    <link href="taskpane.css" rel="stylesheet" type="text/css" />
</head>

<body class="ms-font-m ms-Fabric chat-body">
    <!-- Sideload message -->
    <section id="sideload-msg" class="sideload-message">
        <div class="sideload-content">
            <i class="ms-Icon ms-Icon--Robot ms-font-xxl sideload-icon"></i>
            <h2 class="ms-font-xl">Excel Chat AI</h2>
            <p class="ms-font-m">Please <a target="_blank" href="https://learn.microsoft.com/office/dev/add-ins/testing/test-debug-office-add-ins#sideload-an-office-add-in-for-testing">sideload</a> your add-in to start chatting.</p>
        </div>
    </section>

    <!-- Main chat interface -->
    <main id="app-body" class="chat-container" style="display: none;">
        <!-- Chat header -->
        <header class="chat-header">
            <div class="chat-header-content">
                <i class="ms-Icon ms-Icon--Robot ms-font-l chat-icon"></i>
                <div class="chat-title">
                    <h1 class="ms-font-l">Excel AI Assistant</h1>
                    <span class="ms-font-s chat-status" id="chat-status">Ready to help</span>
                </div>
            </div>
            <button class="ms-Button ms-Button--icon clear-chat-btn" id="clear-chat" title="Clear chat history">
                <i class="ms-Icon ms-Icon--Delete"></i>
            </button>
        </header>

        <!-- Chat messages area -->
        <div class="chat-messages" id="chat-messages">
            <div class="welcome-message">
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="ms-Icon ms-Icon--Robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            Hello! I'm your Excel AI assistant. I can help you with:
                            <ul>
                                <li>Analyzing your spreadsheet data</li>
                                <li>Creating formulas and functions</li>
                                <li>Generating charts and visualizations</li>
                                <li>Data manipulation and formatting</li>
                                <li>Excel tips and best practices</li>
                            </ul>
                            What would you like to work on today?
                        </div>
                        <div class="message-time">Just now</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat input area -->
        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <textarea
                    id="chat-input"
                    class="chat-input ms-font-m"
                    placeholder="Ask me anything about your Excel data..."
                    rows="1"
                    maxlength="2000"></textarea>
                <button
                    id="send-button"
                    class="send-button ms-Button ms-Button--primary"
                    title="Send message (Ctrl+Enter)">
                    <i class="ms-Icon ms-Icon--Send"></i>
                </button>
            </div>
            <div class="input-footer">
                <span class="ms-font-xs">Press Ctrl+Enter to send • <span id="char-count">0</span>/2000</span>
            </div>
        </div>

        <!-- Typing indicator -->
        <div class="typing-indicator" id="typing-indicator" style="display: none;">
            <div class="message ai-message">
                <div class="message-avatar">
                    <i class="ms-Icon ms-Icon--Robot"></i>
                </div>
                <div class="message-content">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>

</html>
