/*
 * AI Integration Module for Excel Chat
 * Handles communication with various AI services
 */

import ExcelDataProcessor from './data-processor.js';

class AIIntegration {
  constructor(config = {}) {
    this.config = {
      provider: config.provider || 'OPENAI_GPT4',
      apiKey: config.apiKey || '',
      endpoint: config.endpoint || '',
      model: config.model || 'gpt-4',
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.7,
      ...config
    };
    
    this.dataProcessor = new ExcelDataProcessor(this.config.provider);
    this.conversationHistory = [];
  }

  // Main method to send Excel data to AI
  async sendToAI(userMessage, options = {}) {
    try {
      // Step 1: Prepare Excel data
      const dataPackage = await this.dataProcessor.prepareDataForAI(userMessage, options);
      
      if (!dataPackage.success) {
        return this.handleDataExtractionError(dataPackage);
      }

      // Step 2: Create AI prompt
      const prompt = this.createPrompt(userMessage, dataPackage.data);
      
      // Step 3: Send to AI service
      const response = await this.callAIService(prompt);
      
      // Step 4: Process and return response
      return this.processAIResponse(response, dataPackage.metadata);
      
    } catch (error) {
      console.error('AI Integration Error:', error);
      return this.createErrorResponse(error);
    }
  }

  // Create optimized prompt for AI
  createPrompt(userMessage, dataPackage) {
    const systemPrompt = this.getSystemPrompt();
    const dataContext = this.formatDataForPrompt(dataPackage);
    const conversationContext = this.getConversationContext();
    
    return {
      system: systemPrompt,
      context: dataContext,
      conversation: conversationContext,
      user: userMessage,
      metadata: {
        timestamp: new Date().toISOString(),
        dataSize: JSON.stringify(dataPackage).length
      }
    };
  }

  getSystemPrompt() {
    return `You are an expert Excel AI assistant. You help users with:
- Excel formulas and functions
- Data analysis and insights
- Chart and visualization recommendations
- Spreadsheet formatting and organization
- Best practices and optimization

Guidelines:
- Provide specific, actionable advice
- Include Excel formulas when relevant
- Explain complex concepts clearly
- Suggest multiple approaches when appropriate
- Consider the user's data context and structure

Always format your responses with:
- Clear headings using **bold**
- Bullet points for lists
- Code blocks for formulas: \`=FORMULA()\`
- Step-by-step instructions when needed`;
  }

  formatDataForPrompt(dataPackage) {
    let context = "Excel Data Context:\n";
    
    // Workbook context
    if (dataPackage.workbookContext) {
      context += `\nWorkbook Info:
- Active Sheet: ${dataPackage.workbookContext.activeSheet}
- Total Sheets: ${dataPackage.workbookContext.worksheetCount}`;
    }

    // Primary data
    if (dataPackage.primaryData) {
      const data = dataPackage.primaryData;
      context += `\nSelected Data:
- Range: ${data.address || 'Unknown'}
- Size: ${data.dimensions?.rows || 0} rows × ${data.dimensions?.columns || 0} columns`;

      // Include sample data if available
      if (data.values && data.values.length > 0) {
        context += `\nData Sample (first few rows):`;
        const sampleRows = data.values.slice(0, 5);
        sampleRows.forEach((row, index) => {
          context += `\nRow ${index + 1}: ${row.slice(0, 5).join(' | ')}`;
        });
      }

      // Include statistics if available
      if (data.statistics && data.statistics.length > 0) {
        context += `\nData Statistics:`;
        data.statistics.slice(0, 3).forEach(stat => {
          context += `\n- ${stat.column}: avg=${stat.avg?.toFixed(2)}, min=${stat.min}, max=${stat.max}`;
        });
      }

      // Include summary
      if (data.summary) {
        context += `\nData Summary: ${data.summary.fillRate}% filled, ${data.summary.nonEmptyCells} non-empty cells`;
      }
    }

    return context;
  }

  getConversationContext() {
    // Include last 3 exchanges for context
    const recentHistory = this.conversationHistory.slice(-6); // 3 exchanges = 6 messages
    
    if (recentHistory.length === 0) return "";
    
    let context = "\nRecent Conversation:";
    recentHistory.forEach((msg, index) => {
      const role = msg.role === 'user' ? 'User' : 'Assistant';
      const content = msg.content.substring(0, 200); // Limit length
      context += `\n${role}: ${content}${msg.content.length > 200 ? '...' : ''}`;
    });
    
    return context;
  }

  // Call different AI services
  async callAIService(prompt) {
    switch (this.config.provider) {
      case 'OPENAI_GPT4':
        return await this.callOpenAI(prompt);
      case 'AZURE_AI':
        return await this.callAzureAI(prompt);
      case 'CLAUDE':
        return await this.callClaude(prompt);
      case 'MOCK':
        return await this.callMockAI(prompt);
      default:
        throw new Error(`Unsupported AI provider: ${this.config.provider}`);
    }
  }

  async callOpenAI(prompt) {
    const messages = [
      { role: 'system', content: prompt.system },
      { role: 'user', content: `${prompt.context}\n\n${prompt.conversation}\n\nUser Question: ${prompt.user}` }
    ];

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.choices[0].message.content,
      usage: data.usage,
      model: data.model
    };
  }

  async callAzureAI(prompt) {
    // Azure OpenAI implementation
    const endpoint = this.config.endpoint;
    const apiVersion = '2023-12-01-preview';
    
    const response = await fetch(`${endpoint}/openai/deployments/${this.config.model}/chat/completions?api-version=${apiVersion}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': this.config.apiKey
      },
      body: JSON.stringify({
        messages: [
          { role: 'system', content: prompt.system },
          { role: 'user', content: `${prompt.context}\n\n${prompt.user}` }
        ],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature
      })
    });

    if (!response.ok) {
      throw new Error(`Azure AI error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.choices[0].message.content,
      usage: data.usage,
      model: this.config.model
    };
  }

  async callClaude(prompt) {
    // Anthropic Claude implementation
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: this.config.model || 'claude-3-sonnet-20240229',
        max_tokens: this.config.maxTokens,
        system: prompt.system,
        messages: [
          { role: 'user', content: `${prompt.context}\n\n${prompt.user}` }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`Claude API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.content[0].text,
      usage: data.usage,
      model: data.model
    };
  }

  // Mock AI for testing
  async callMockAI(prompt) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const userMessage = prompt.user.toLowerCase();
    let response = "";

    if (userMessage.includes('formula') || userMessage.includes('function')) {
      response = `Based on your Excel data, here are some formula suggestions:

**For your selected range:**
- \`=SUM(${prompt.context.includes('Range:') ? prompt.context.split('Range: ')[1].split('\n')[0] : 'A1:A10'})\` - Sum all values
- \`=AVERAGE(${prompt.context.includes('Range:') ? prompt.context.split('Range: ')[1].split('\n')[0] : 'A1:A10'})\` - Calculate average
- \`=COUNT(${prompt.context.includes('Range:') ? prompt.context.split('Range: ')[1].split('\n')[0] : 'A1:A10'})\` - Count numbers

**Advanced Options:**
- Use \`=SUMIF()\` for conditional sums
- Try \`=VLOOKUP()\` for data lookups
- Consider \`=PIVOT()\` for data analysis`;

    } else if (userMessage.includes('chart') || userMessage.includes('graph')) {
      response = `**Chart Recommendations for Your Data:**

Based on your data structure, I recommend:

1. **Column Chart** - Great for comparing values across categories
2. **Line Chart** - Perfect for showing trends over time
3. **Pie Chart** - Use if you want to show parts of a whole

**Steps to Create:**
1. Select your data range
2. Go to Insert > Charts
3. Choose the chart type
4. Customize colors and labels`;

    } else if (userMessage.includes('analyze') || userMessage.includes('data')) {
      response = `**Data Analysis Results:**

Your dataset shows:
- **Size:** Multiple rows and columns of data
- **Data Types:** Mix of numbers and text
- **Completeness:** Good data coverage

**Key Insights:**
- Consider using pivot tables for deeper analysis
- Look for patterns in your numeric columns
- Check for any data quality issues

**Next Steps:**
- Apply filters to explore subsets
- Create summary statistics
- Consider data visualization`;

    } else {
      response = `I can help you with your Excel task! Based on your data context, here are some suggestions:

**What I can help with:**
- Formula creation and troubleshooting
- Data analysis and insights
- Chart and visualization guidance
- Formatting and organization tips

**Your Current Data:**
${prompt.context.substring(0, 200)}...

What specific Excel task would you like assistance with?`;
    }

    return {
      content: response,
      usage: { total_tokens: response.length / 4 }, // Rough estimate
      model: 'mock-ai'
    };
  }

  processAIResponse(response, metadata) {
    // Add to conversation history
    this.conversationHistory.push(
      { role: 'assistant', content: response.content, timestamp: new Date().toISOString() }
    );

    // Keep history manageable
    if (this.conversationHistory.length > 20) {
      this.conversationHistory = this.conversationHistory.slice(-20);
    }

    return {
      success: true,
      content: response.content,
      metadata: {
        ...metadata,
        aiModel: response.model,
        usage: response.usage,
        responseLength: response.content.length
      }
    };
  }

  handleDataExtractionError(dataPackage) {
    return {
      success: false,
      content: "I'm having trouble accessing your Excel data. Please make sure you have a range selected and try again.",
      error: dataPackage.error,
      fallback: dataPackage.fallback
    };
  }

  createErrorResponse(error) {
    return {
      success: false,
      content: "I encountered an error while processing your request. Please try again or rephrase your question.",
      error: error.message
    };
  }

  // Utility methods
  addToHistory(role, content) {
    this.conversationHistory.push({
      role,
      content,
      timestamp: new Date().toISOString()
    });
  }

  clearHistory() {
    this.conversationHistory = [];
  }

  getHistory() {
    return this.conversationHistory;
  }
}

export default AIIntegration;
