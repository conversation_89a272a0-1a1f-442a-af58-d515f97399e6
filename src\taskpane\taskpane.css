/* 
 * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
 * See LICENSE in the project root for license information.
 */

 html,
 body {
     width: 100%;
     height: 100%;
     margin: 0;
     padding: 0;
 }
 
 ul {
     margin: 0;
     padding: 0;
 }
 
 .ms-welcome__header {
    padding: 20px;
    padding-bottom: 30px;
    padding-top: 100px;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    align-items: center;
 }

 .ms-welcome__main {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-align-items: center;
    align-items: center;
    -webkit-flex: 1 0 0;
    flex: 1 0 0;
    padding: 10px 20px;
 }
 
 .ms-welcome__main > h2 {
     width: 100%;
     text-align: center;
 }
 
 .ms-welcome__features {
     list-style-type: none;
     margin-top: 20px;
 }
 
 .ms-welcome__features.ms-List .ms-ListItem {
     padding-bottom: 20px;
     display: -webkit-flex;
     display: flex;
 }
 
 .ms-welcome__features.ms-List .ms-ListItem > .ms-Icon {
     margin-right: 10px;
 }
 
 .ms-welcome__action.ms-Button--hero {
     margin-top: 30px;
 }
 
.ms-Button.ms-Button--hero .ms-Button-label {
  color: #0078d7;
}

.ms-Button.ms-Button--hero:hover .ms-Button-label,
.ms-Button.ms-Button--hero:focus .ms-Button-label{
  color: #005a9e;
  cursor: pointer;
}

b {
    font-weight: bold;
}