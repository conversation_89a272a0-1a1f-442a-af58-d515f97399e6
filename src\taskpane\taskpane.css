/*
 * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
 * Excel Chat AI Add-in Styles
 */

/* Base styles */
html,
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto", "Helvetica Neue", sans-serif;
    background-color: #f8f9fa;
}

.chat-body {
    height: 100vh;
    overflow: hidden;
}

/* Sideload message styles */
.sideload-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.sideload-content {
    padding: 2rem;
    max-width: 300px;
}

.sideload-icon {
    color: white;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.sideload-message h2 {
    margin: 1rem 0;
    font-weight: 600;
}

.sideload-message a {
    color: #e3f2fd;
    text-decoration: underline;
}

.sideload-message a:hover {
    color: white;
}

/* Chat container */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #ffffff;
}

/* Chat header */
.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: linear-gradient(90deg, #0078d4 0%, #106ebe 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.chat-header-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chat-icon {
    color: white;
    opacity: 0.9;
}

.chat-title h1 {
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.chat-status {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    margin-top: 0.2rem;
}

.clear-chat-btn {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-chat-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Chat messages area */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background-color: #f8f9fa;
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Welcome message */
.welcome-message {
    margin-bottom: 1rem;
}

/* Message styles */
.message {
    display: flex;
    margin-bottom: 1rem;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.ai-message .message-avatar {
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    color: white;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #16a085 0%, #27ae60 100%);
    color: white;
}

.message-content {
    flex: 1;
    max-width: calc(100% - 48px);
}

.message-text {
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    line-height: 1.4;
    word-wrap: break-word;
}

.ai-message .message-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.user-message .message-text {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
}

.message-text ul {
    margin: 0.5rem 0;
    padding-left: 1.2rem;
}

.message-text li {
    margin-bottom: 0.3rem;
}

.message-time {
    font-size: 0.7rem;
    color: #6c757d;
    margin-top: 0.3rem;
    padding-left: 1rem;
}

/* User messages alignment */
.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    margin-right: 0;
    margin-left: 0.75rem;
}

.user-message .message-time {
    text-align: right;
    padding-left: 0;
    padding-right: 1rem;
}

/* Chat input container */
.chat-input-container {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    flex-shrink: 0;
}

.chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 0.5rem;
    transition: border-color 0.2s ease;
}

.chat-input-wrapper:focus-within {
    border-color: #0078d4;
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.chat-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    min-height: 20px;
    max-height: 120px;
    padding: 0.5rem;
    font-family: inherit;
    line-height: 1.4;
}

.chat-input::placeholder {
    color: #6c757d;
}

.send-button {
    background: #0078d4;
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
}

.send-button:hover:not(:disabled) {
    background: #106ebe;
}

.send-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    color: #6c757d;
}

/* Typing indicator */
.typing-indicator {
    padding: 0 1rem;
    background-color: #f8f9fa;
}

.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0.75rem 1rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #6c757d;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive design */
@media (max-width: 480px) {
    .chat-header {
        padding: 0.75rem 1rem;
    }

    .chat-messages {
        padding: 0.75rem;
    }

    .chat-input-container {
        padding: 0.75rem 1rem;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
    }
}